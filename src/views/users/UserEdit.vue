<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { Form, FormSubmitEvent } from '@primevue/forms';
import { yupResolver } from '@primevue/forms/resolvers/yup';
import * as yup from 'yup';
import { useUsersStore } from '@/stores/users';
import { useTranslation } from 'i18next-vue';
import { UpdateUserInput, PrivilegeOption, User } from '@/services/users/types';
import { ApiResponse } from '@/services/types';
import { mapErrorCode } from '@/utilities/ErrorCodeMapper';

const { t } = useTranslation();
const usersStore = useUsersStore();

// Props
const props = defineProps<{
    user: User;
}>();

// Emits
const emit = defineEmits<{
    userUpdated: [];
    cancel: [];
}>();

// Initial values for the form
const initialValues = {
    password: '',
    confirmPassword: '',
    privileges: [...props.user.privileges]
};

// State
const loading = ref(false);
const backendError = ref('');
const privilegeOptions = ref<PrivilegeOption[]>([]);
const changePasswordEnabled = ref(false);

// Validation schema - password validation only applies when toggle is enabled
const resolver = computed(() =>
    yupResolver(
        yup.object().shape({
            password: changePasswordEnabled.value ? yup.string().required('Password is required.').min(6, 'Password must be at least 6 characters.') : yup.string(),
            confirmPassword: changePasswordEnabled.value
                ? yup
                      .string()
                      .required('Please confirm your password.')
                      .oneOf([yup.ref('password')], 'Passwords must match.')
                : yup.string(),
            privileges: yup.array().min(1, 'At least one privilege must be selected.')
        })
    )
);

// Computed properties
const privilegesByGroup = computed(() => {
    const groups: { [key: string]: PrivilegeOption[] } = {};
    privilegeOptions.value.forEach((option) => {
        if (!groups[option.group]) {
            groups[option.group] = [];
        }
        groups[option.group].push(option);
    });
    return groups;
});

// Load privilege options on mount
onMounted(async () => {
    await loadPrivilegeOptions();
});

// Form reference to access form methods
const formRef = ref();

// Watch for password toggle changes
watch(changePasswordEnabled, (enabled) => {
    if (!enabled && formRef.value) {
        // Clear password fields when toggle is disabled
        formRef.value.setFieldValue('password', '');
        formRef.value.setFieldValue('confirmPassword', '');
    }
});

async function loadPrivilegeOptions() {
    const response = await usersStore.dispatchGetPrivilegeOptions(props.user.id);
    if (response.success && response.content) {
        privilegeOptions.value = response.content;
    }
}

// Handle form submission
async function handleSubmit(e: FormSubmitEvent) {
    backendError.value = '';

    if (!e.valid) {
        return;
    }

    loading.value = true;

    const updateUserInput: UpdateUserInput = {
        id: props.user.id,
        privileges: e.values.privileges
    };

    // Only include password if toggle is enabled and password was provided
    if (changePasswordEnabled.value && e.values.password && e.values.password.trim().length > 0) {
        updateUserInput.password = e.values.password;
    }

    const response: ApiResponse<any> = await usersStore.dispatchUpdateUser(updateUserInput);

    loading.value = false;

    if (response.success) {
        emit('userUpdated');
    } else {
        backendError.value = mapErrorCode(response.errorResponse?.codeName);
    }
}

function handleCancel() {
    emit('cancel');
}
</script>

<template>
    <div class="user-edit-form">
        <Form ref="formRef" v-slot="$form" :resolver="resolver" :initialValues="initialValues" @submit="handleSubmit">
            <div class="grid grid-cols-12 gap-6">
                <!-- User Info Display -->
                <div class="col-span-12">
                    <div class="flex items-center gap-4 pb-4 border-b border-surface-200">
                        <Avatar v-if="user.profilePictureUrl" :image="user.profilePictureUrl" shape="circle" size="large" />
                        <Avatar v-else :label="user.username.charAt(0).toUpperCase()" shape="circle" size="large" />
                        <div>
                            <h4 class="m-0 text-lg font-semibold">{{ user.username }}</h4>
                            <Tag :value="user.role" class="mt-1" />
                        </div>
                    </div>
                </div>

                <!-- Change Password Toggle -->
                <div class="col-span-12">
                    <div class="flex items-center gap-3">
                        <ToggleSwitch v-model="changePasswordEnabled" inputId="changePassword" />
                        <label for="changePassword" class="text-surface-900 dark:text-surface-0 font-medium">
                            {{ t('changePassword') }}
                        </label>
                    </div>
                </div>

                <!-- Password Fields (Only shown when toggle is enabled) -->
                <template v-if="changePasswordEnabled">
                    <!-- Password -->
                    <div class="col-span-12 md:col-span-6">
                        <label for="password" class="block text-surface-900 dark:text-surface-0 font-medium mb-2"> {{ t('newPassword') }} * </label>
                        <Password id="password" name="password" :placeholder="t('password')" class="mb-1" toggleMask fluid />
                        <div class="h-6 relative">
                            <Message v-if="$form.password?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                                {{ $form.password.error.message }}
                            </Message>
                        </div>
                    </div>

                    <!-- Confirm Password -->
                    <div class="col-span-12 md:col-span-6">
                        <label for="confirmPassword" class="block text-surface-900 dark:text-surface-0 font-medium mb-2"> {{ t('confirmPassword') }} * </label>
                        <Password id="confirmPassword" name="confirmPassword" :placeholder="t('confirmPassword')" class="mb-1" toggleMask fluid />
                        <div class="h-6 relative">
                            <Message v-if="$form.confirmPassword?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                                {{ $form.confirmPassword.error.message }}
                            </Message>
                        </div>
                    </div>
                </template>

                <!-- Privileges -->
                <div class="col-span-12">
                    <label class="block text-surface-900 dark:text-surface-0 font-medium mb-3"> {{ t('privileges') }} * </label>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div v-for="(groupOptions, groupName) in privilegesByGroup" :key="groupName" class="privilege-group">
                            <h6 class="text-sm font-semibold text-surface-600 mb-2">{{ t(groupName) }}</h6>
                            <div class="flex flex-col gap-2">
                                <div v-for="option in groupOptions" :key="option.name" class="flex items-center">
                                    <Checkbox :inputId="option.name" name="privileges" :value="option.name" :disabled="option.disabled" />
                                    <label :for="option.name" class="ml-2 text-sm" :class="{ 'text-surface-400': option.disabled }">
                                        {{ t(option.name) }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="h-6 relative mt-2">
                        <Message v-if="$form.privileges?.invalid" severity="error" size="small" variant="simple" class="absolute top-1">
                            {{ $form.privileges.error.message }}
                        </Message>
                    </div>
                </div>

                <!-- Backend Error -->
                <div class="col-span-12" v-if="backendError">
                    <Message severity="error" class="mb-4">{{ backendError }}</Message>
                </div>

                <!-- Action Buttons -->
                <div class="col-span-12">
                    <div class="flex justify-end gap-2">
                        <Button type="button" :label="t('cancel')" severity="secondary" @click="handleCancel" />
                        <Button type="submit" :label="t('updateUser')" :loading="loading" />
                    </div>
                </div>
            </div>
        </Form>
    </div>
</template>
